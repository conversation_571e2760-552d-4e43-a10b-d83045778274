#include <iostream>
#include <cstdlib>
#include <array>
#include <random>
#include <ctime>

// Function declarations
float *addarr(float A[], float B[], int N);
float *generateRandomArray(int N, float min = 0.0f, float max = 10.0f);
void testRandomArrays();

int main()
{
    std::cout << "\n--- Testing Random Arrays ---\n";
    testRandomArrays();
    return 0;
}

float *addarr(float A[], float B[], int N)
{
    float *C = (float *)malloc(N * sizeof(float));
    for (int i = 0; i < N; i++)
        C[i] = A[i] + B[i];
    return C;
}

float *generateRandomArray(int N, float min, float max)
{
    // Seed the random number generator
    static bool seeded = false;
    if (!seeded) {
        std::srand(std::time(nullptr));
        seeded = true;
    }

    float *arr = (float *)malloc(N * sizeof(float));
    if (arr == nullptr) {
        std::cerr << "Memory allocation failed!" << std::endl;
        return nullptr;
    }

    for (int i = 0; i < N; i++) {
        // Generate random float between min and max
        float random = (float)std::rand() / RAND_MAX; // 0.0 to 1.0
        arr[i] = min + random * (max - min);
    }

    return arr;
}

void testRandomArrays(int N=50)
{
    std::cout << "Testing random arrays of size " << N << std::endl;
    std::cout << "Generating two random arrays of size " << N << std::endl;

    float *A = generateRandomArray(N, 1.0f, 5.0f);
    float *B = generateRandomArray(N, 2.0f, 8.0f);

    if (A == nullptr || B == nullptr) {
        std::cerr << "Failed to generate random arrays!" << std::endl;
        return;
    }

    std::cout << "Array A: ";
    for (int i = 0; i < N; i++) {
        std::cout << A[i] << " ";
    }
    std::cout << std::endl;

    std::cout << "Array B: ";
    for (int i = 0; i < N; i++) {
        std::cout << B[i] << " ";
    }
    std::cout << std::endl;

    float *C = addarr(A, B, N);
    std::cout << "Sum A+B: ";
    for (int i = 0; i < N; i++) {
        std::cout << C[i] << " ";
    }
    std::cout << std::endl;

    // Clean up memory
    free(A);
    free(B);
    free(C);
}
