# Array Adder - Performance Testing Tool

A C++ program that performs element-wise addition of floating-point arrays with performance benchmarking capabilities.

## Overview

This program demonstrates basic array operations in C++ with a focus on performance measurement. It generates random floating-point arrays of increasing sizes and measures the time taken to perform element-wise addition operations.

## Features

- **Array Addition**: Element-wise addition of two floating-point arrays
- **Random Array Generation**: Creates arrays filled with random floats within specified ranges
- **Performance Timing**: High-resolution timing of array operations using `std::chrono`
- **Memory Management**: Proper dynamic memory allocation and deallocation
- **Scalable Testing**: Tests arrays of exponentially increasing sizes (powers of 10)
- **Selective Output**: Only displays array contents for power-of-2 sizes to reduce output clutter

## Functions

### Core Functions

- `float* addarr(float A[], float B[], int N)`: Performs element-wise addition of two arrays
- `float* generateRandomArray(int N, float min, float max)`: Generates an array of random floats
- `void printArray(const char* label, float* arr, int N)`: Prints array contents (filtered for powers of 2)
- `float* timed(function_ptr, A, B, N)`: Wrapper function that times array operations

### Test Functions

- `void testRandomArrays(int N)`: Main testing function that generates arrays and measures performance
- `int main()`: Runs performance tests with exponentially increasing array sizes

## Compilation

```bash
g++ -o arrayAdder arrayAdder.cpp
```

Or with optimization flags:
```bash
g++ -O2 -o arrayAdder arrayAdder.cpp
```

## Usage

Simply run the compiled executable:

```bash
./arrayAdder
```

The program will automatically:
1. Start with arrays of size 2
2. Increase size by factor of 10 each iteration (2, 20, 200, 2000, ...)
3. Generate two random arrays for each size
4. Perform element-wise addition
5. Display timing information
6. Show array contents only for power-of-2 sizes

## Sample Output

```
--- Testing Random Arrays ---
Iteration: 2 - Testing random arrays of size 2
Generating two random arrays of size 2
Array A: 3.45 2.78
Array B: 6.12 4.33
Time: 15 microseconds
Sum A+B: 9.57 7.11

Iteration: 20 - Testing random arrays of size 20
Generating two random arrays of size 20
Time: 45 microseconds

Iteration: 200 - Testing random arrays of size 200
Generating two random arrays of size 200
Time: 234 microseconds
...
```

## Performance Characteristics

- **Array A Range**: Random floats between 1.0 and 5.0
- **Array B Range**: Random floats between 2.0 and 8.0
- **Timing Precision**: Microsecond resolution using `std::chrono::high_resolution_clock`
- **Memory**: Dynamic allocation using `malloc()` with proper cleanup

## Technical Details

### Memory Management
- Uses `malloc()` for dynamic memory allocation
- Includes proper error checking for allocation failures
- Ensures all allocated memory is freed with `free()`

### Random Number Generation
- Uses `std::rand()` with time-based seeding
- Generates uniformly distributed floats within specified ranges
- Seeds only once per program execution

### Output Filtering
- Array contents are only displayed when array size is a power of 2
- Uses bit manipulation `(N & (N - 1)) == 0` to detect powers of 2
- Reduces output volume for large-scale performance testing

## Requirements

- C++11 or later (for `std::chrono`)
- Standard C++ library
- Compatible with GCC, Clang, and MSVC compilers

## Potential Use Cases

- Performance benchmarking of array operations
- Memory allocation performance testing
- Baseline measurements for GPU acceleration comparisons
- Educational tool for understanding C++ memory management and timing

## Notes

- The program will run for a very long time as it approaches `INT32_MAX`
- Consider modifying the loop condition in `main()` for shorter test runs
- Array contents are only shown for power-of-2 sizes to keep output manageable
- All timing measurements are in microseconds
